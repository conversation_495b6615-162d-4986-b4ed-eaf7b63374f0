package ui

import (
	"deskcrm/api/allocate"
	"deskcrm/api/coursetransgo"
	"deskcrm/api/dal"
	"deskcrm/api/dataproxy"
	"deskcrm/api/moat"
	"deskcrm/api/tower"
	"fmt"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// CourseRecordMetaService 课程记录元数据服务
// 对应PHP的Service_Page_DeskV1_Student_CourseRecordMeta
type CourseRecordMetaService struct {
	allocateClient  *allocate.Client
	moatClient      *moat.Client
	dataproxyClient *dataproxy.Client
	towerClient     *tower.Client
}

// NewCourseRecordMetaService 创建课程记录元数据服务实例
func NewCourseRecordMetaService() *CourseRecordMetaService {
	return &CourseRecordMetaService{
		allocateClient:  allocate.NewClient(),
		moatClient:      moat.NewClient(),
		dataproxyClient: dataproxy.NewClient(),
		towerClient:     tower.NewClient(),
	}
}

// CourseRecordMetaRequest 请求参数
type CourseRecordMetaRequest struct {
	StudentUid int64 `json:"studentUid" form:"studentUid" binding:"required"` // 学生UID
	PersonUid  int64 `json:"personUid" form:"personUid" binding:"required"`   // 人员UID（LPC）
	CourseId   int64 `json:"courseId" form:"courseId" binding:"required"`     // 课程ID
}

// CourseRecordMetaResponse 响应数据
type CourseRecordMetaResponse struct {
	TotalCourses        int    `json:"totalCourses"`        // 总课程数
	CurrentPeriodCourse int    `json:"currentPeriodCourse"` // 当前期课程数
	TotalPaymentAmount  string `json:"totalPaymentAmount"`  // 总付费金额（格式化字符串）
	AttendedLessons     int    `json:"attendedLessons"`     // 已上课节数
	FinishedLessons     int    `json:"finishedLessons"`     // 已完成课节数
	PlaybackLessons     int    `json:"playbackLessons"`     // 回放课节数
}

// Execute 执行课程记录元数据查询
// 对应PHP的Service_Page_DeskV1_Student_CourseRecordMeta::execute方法
func (s *CourseRecordMetaService) Execute(ctx *gin.Context, req CourseRecordMetaRequest) (*CourseRecordMetaResponse, error) {
	customUid := req.StudentUid
	scUid := req.PersonUid
	currentCourseId := req.CourseId

	zlog.Infof(ctx, "CourseRecordMeta Execute start, customUid: %d, scUid: %d, currentCourseId: %d",
		customUid, scUid, currentCourseId)

	// 1. 首先获取学生的课程信息
	// 由于我们需要课程ID列表，先从当前课程开始，然后通过其他方式获取完整列表
	// 这里简化处理，使用当前课程ID作为起点
	allCourseIds := []int64{currentCourseId}

	// 通过GetLeadsByBatchCourseIdUid获取学生的leads信息
	leadsInfo, err := s.allocateClient.GetLeadsByBatchCourseIdUid(ctx, allCourseIds, customUid)
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByBatchCourseIdUid failed: %v", err)
		// 如果获取失败，继续使用当前课程ID
		leadsInfo = []allocate.BatchLeadsInfo{}
	}

	// 从leads信息中提取所有课程ID
	courseIdSet := make(map[int64]bool)
	courseIdSet[currentCourseId] = true // 确保包含当前课程

	for _, lead := range leadsInfo {
		if !courseIdSet[lead.CourseId] {
			allCourseIds = append(allCourseIds, lead.CourseId)
			courseIdSet[lead.CourseId] = true
		}
	}

	// 2. 获取课程基本信息
	courseLessonMap, err := dal.GetCourseLessonInfoByCourseIds(ctx, allCourseIds)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseLessonInfoByCourseIds failed: %v", err)
		return nil, fmt.Errorf("获取课程信息失败: %v", err)
	}

	// 3. 获取课程过期时间信息
	expireTimeMap, err := s.towerClient.GetBatchExpireTimeByCourseList(ctx, allCourseIds)
	if err != nil {
		zlog.Warnf(ctx, "GetBatchExpireTimeByCourseList failed: %v", err)
		// 过期时间获取失败不影响主流程，继续执行
		expireTimeMap = make(map[int64]int64)
	}

	// 4. 获取付费信息 - 通过moat客户端调用billing接口
	orderList, err := s.getOrderListFromMoat(ctx, customUid)
	if err != nil {
		zlog.Warnf(ctx, "GetOrderList failed: %v", err)
		// 付费信息获取失败不影响主流程，继续执行
		orderList = []moat.TradeDetailItem{}
	}

	// 5. 获取转课数据
	transData, err := coursetransgo.NewClient().GetCourseTransListByStaffStudent(ctx, scUid, customUid, allCourseIds)
	if err != nil {
		zlog.Warnf(ctx, "GetTransDataBySc failed: %v", err)
		// 转课数据获取失败不影响主流程，继续执行
		transData = []coursetransgo.ScTransData{}
	}

	// 6. 获取LPC leads数据（回放和完成课节数）
	lpcLeadsData, err := s.dataproxyClient.GetIdlLpcLeadsDataByStudentUids(ctx, []int64{customUid},
		[]string{"playback_num", "finish_num"})
	if err != nil {
		zlog.Warnf(ctx, "GetIdlLpcLeadsDataByStudentUids failed: %v", err)
		// LPC数据获取失败不影响主流程，继续执行
		lpcLeadsData = []*dataproxy.IdlLpcLeadsData{}
	}

	// 7. 计算元数据
	metaData := s.calculateMetaData(ctx, customUid, scUid, currentCourseId,
		leadsInfo, expireTimeMap, courseLessonMap, orderList, transData, lpcLeadsData)

	zlog.Infof(ctx, "CourseRecordMeta Execute completed, result: %+v", metaData)
	return metaData, nil
}

// calculateMetaData 计算课程记录元数据
func (s *CourseRecordMetaService) calculateMetaData(
	ctx *gin.Context,
	customUid int64,
	scUid int64,
	currentCourseId int64,
	leadsInfo []allocate.BatchLeadsInfo,
	expireTimeMap map[int64]int64,
	courseLessonMap map[string]dal.CourseInfo,
	orderList []moat.TradeDetailItem,
	transData []coursetransgo.ScTransData,
	lpcLeadsData []*dataproxy.IdlLpcLeadsData,
) *CourseRecordMetaResponse {

	result := &CourseRecordMetaResponse{}

	// 计算总课程数
	courseSet := make(map[int64]bool)
	for _, lead := range leadsInfo {
		courseSet[lead.CourseId] = true
	}
	result.TotalCourses = len(courseSet)

	// 计算当前期课程数
	result.CurrentPeriodCourse = s.calculateCurrentPeriodCourses(ctx, currentCourseId, courseLessonMap, leadsInfo)

	// 计算总付费金额
	result.TotalPaymentAmount = s.calculatePaymentAmount(ctx, customUid, orderList, transData)

	// 计算课节统计信息
	attendedLessons, finishedLessons, playbackLessons := s.calculateLessonStats(
		ctx, customUid, currentCourseId, lpcLeadsData, courseLessonMap)

	result.AttendedLessons = attendedLessons
	result.FinishedLessons = finishedLessons
	result.PlaybackLessons = playbackLessons

	return result
}

// calculateCurrentPeriodCourses 计算当前期课程数
// 根据课程的期次信息计算学生在当前期的课程数量
func (s *CourseRecordMetaService) calculateCurrentPeriodCourses(
	ctx *gin.Context,
	currentCourseId int64,
	courseLessonMap map[string]dal.CourseInfo,
	leadsInfo []allocate.BatchLeadsInfo,
) int {
	// 获取当前课程的期次信息
	currentCourseKey := fmt.Sprintf("%d", currentCourseId)
	currentCourse, exists := courseLessonMap[currentCourseKey]
	if !exists {
		return 1 // 如果找不到当前课程信息，默认返回1
	}

	// 统计学生在相同期次的课程数量
	currentPeriodCount := 0
	studentCourseIds := make(map[int64]bool)

	// 先获取学生的所有课程ID
	for _, lead := range leadsInfo {
		studentCourseIds[lead.CourseId] = true
	}

	// 统计学生在当前期次的课程数量
	for courseIdStr, course := range courseLessonMap {
		courseId, parseErr := strconv.ParseInt(courseIdStr, 10, 64)
		if parseErr != nil {
			continue
		}

		// 检查是否是学生的课程，且在相同期次
		if studentCourseIds[courseId] && course.Season == currentCourse.Season && course.Year == currentCourse.Year {
			currentPeriodCount++
		}
	}

	// 如果没有找到任何课程，至少返回1（当前课程）
	if currentPeriodCount == 0 {
		currentPeriodCount = 1
	}

	return currentPeriodCount
}

// calculatePaymentAmount 计算付费金额
// 根据订单信息和转课数据计算实际付费金额，返回格式化的字符串
func (s *CourseRecordMetaService) calculatePaymentAmount(
	ctx *gin.Context,
	customUid int64,
	orderList []moat.TradeDetailItem,
	transData []coursetransgo.ScTransData,
) string {
	var totalAmount float64

	// 计算订单总金额
	for _, order := range orderList {
		if order.UserId == customUid && order.OrderStatus == 1 { // 已支付状态
			totalAmount += float64(order.PaidAmount) / 100.0 // 转换为元
		}
	}

	// 减去退费金额
	for _, trans := range transData {
		if trans.CustomUid == customUid && trans.Refund > 0 {
			totalAmount -= float64(trans.Refund) / 100.0 // 转换为元
		}
	}

	// 确保金额不为负数
	if totalAmount < 0 {
		totalAmount = 0
	}

	// 格式化为两位小数的字符串
	return fmt.Sprintf("%.2f", totalAmount)
}

// calculateLessonStats 计算课节统计信息
// 根据LPC数据和课程信息计算各种课节数量
// 注意：统计学生的所有课程的课节数，不仅仅是当前课程
func (s *CourseRecordMetaService) calculateLessonStats(
	ctx *gin.Context,
	customUid int64,
	currentCourseId int64,
	lpcLeadsData []*dataproxy.IdlLpcLeadsData,
	courseLessonMap map[string]dal.CourseInfo,
) (attendedLessons, finishedLessons, playbackLessons int) {

	// 从LPC数据中统计学生的所有课程课节数
	for _, lpcData := range lpcLeadsData {
		if lpcData.StudentUid == customUid {
			finishedLessons += int(lpcData.FinishNum)
			playbackLessons += int(lpcData.PlaybackNum)
		}
	}

	// 已上课节数 = 已完成课节数 + 回放课节数
	// 根据业务逻辑，已上课节数应该包含所有形式的上课
	attendedLessons = finishedLessons + playbackLessons

	return attendedLessons, finishedLessons, playbackLessons
}

// getOrderListFromMoat 通过moat客户端获取订单列表
// 对应PHP的Api_Billing::orderList方法，实际通过moat服务调用
func (s *CourseRecordMetaService) getOrderListFromMoat(ctx *gin.Context, customUid int64) ([]moat.TradeDetailItem, error) {
	// 设置时间范围：最近一年
	now := time.Now().Unix()
	startTime := now - 365*86400 // 一年前
	endTime := now

	// 首先获取订单列表
	tradeList, err := s.moatClient.GetTradeList(ctx, customUid, []int{}, startTime, endTime, 0, 100, []int64{}, []map[string]string{})
	if err != nil {
		return nil, fmt.Errorf("GetTradeList failed: %w", err)
	}

	if len(tradeList.List) == 0 {
		return []moat.TradeDetailItem{}, nil
	}

	// 提取订单ID
	orderIds := make([]int64, 0, len(tradeList.List))
	for _, item := range tradeList.List {
		orderIds = append(orderIds, item.OrderId)
	}

	// 获取订单详情
	fields := []string{"userId", "orderId", "orderTime", "payTime", "businessType", "payableAmount", "paidAmount", "orderStatus"}
	orderDetails, err := s.moatClient.GetTradeDetail(ctx, customUid, orderIds, fields)
	if err != nil {
		return nil, fmt.Errorf("GetTradeDetail failed: %w", err)
	}

	return orderDetails, nil
}
