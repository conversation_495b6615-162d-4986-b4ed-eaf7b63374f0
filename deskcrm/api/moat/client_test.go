package moat

import (
	"deskcrm/helpers"
	"deskcrm/util"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {

	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()

	// 初始化API客户端配置
	helpers.InitApiClient()
}

func TestClient_GetSkuListByCondition(t *testing.T) {
	courseId := int64(546302)
	conds := map[string]interface{}{
		"op": "and",
		"aggs": []map[string]interface{}{
			{
				"type": 0,
				"conds": map[string]interface{}{
					"key":   "thirdId",
					"value": []int64{courseId},
					"exps":  "in",
				},
			},
			{
				"type": 0,
				"conds": map[string]interface{}{
					"key":   "skuMode",
					"value": 1, // SKU_MODE_COURSE
					"exps":  "eq",
				},
			},
		},
	}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	skuList, err := NewClient().GetSkuListByCondition(ctx, conds, nil, 0, 1, []string{"skuId", "thirdId"}, nil)
	for _, item := range skuList {
		fmt.Printf("skuId: %d\n", int64(item.SkuId))

	}
	assert.NoError(t, err)
	assert.NotNil(t, skuList)
}

func TestClient_GetOneOpenSearch(t *testing.T) {
	studentIds := []int64{2135507584}
	skuIds := []int64{6321138}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	orderMap, err := NewClient().GetOneOpenSearch(ctx, studentIds, skuIds)
	assert.NoError(t, err)
	assert.NotNil(t, orderMap)
	fmt.Printf("orderMap: %v\n", orderMap)
}

func TestClient_GetThirdOrderSearch(t *testing.T) {
	orderIds := []int64{2106177278}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	orderMap, err := NewClient().GetThirdOrderSearch(ctx, orderIds)
	assert.NoError(t, err)

	// 只有在没有错误且有数据时才处理
	if len(orderMap) > 0 {
		result := "无需填写"
		orderInfo := orderMap[0]
		addressStatus := orderInfo.DeliveryAddressStatus
		if addressStatus == 1 {
			result = "https://cube.zuoyebang.com/static/cube-extend/receiveAddressFormV2.html?orderId=" + util.GetStringValue(orderInfo.Extra, "orderNo")
		}
		if addressStatus == 2 {
			result = util.GetStringValue(orderInfo.Extra, "receiverState") + util.GetStringValue(orderInfo.Extra, "receiverCity") + util.GetStringValue(orderInfo.Extra, "receiverDistrict")
		}
		fmt.Printf("result: %s\n", result)
	}
	fmt.Printf("orderMap: %v\n", orderMap)
}

func TestClient_BatchGetUserRoles(t *testing.T) {
	studentUids := []int64{2135507584, 2135307214}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	userRoles := NewClient().BatchGetUserRoles(ctx, studentUids)
	assert.NotNil(t, userRoles)
	fmt.Printf("userRoles: %v\n", userRoles)
}

func TestClient_GetSkuBaseInfo(t *testing.T) {
	skuIds := []int64{6321138, 6321139}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	skuInfoList, err := NewClient().GetSkuBaseInfo(ctx, skuIds)
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("skuInfoList: %v\n", skuInfoList)
}

func TestClient_AfterDetail(t *testing.T) {

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	afterDetail, err := NewClient().AfterDetail(ctx, 2135307214, []string{"101440468"}, []string{"refund"})
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("afterDetail: %v\n", afterDetail)
}

func TestClient_GetGoodsSkuKVBySkuId(t *testing.T) {
	skuIds := []int64{6321138, 6321139}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	skuInfoMap, err := NewClient().GetGoodsSkuKVBySkuId(ctx, skuIds)
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("skuInfoMap: %v\n", skuInfoMap)
}

func TestClient_Search(t *testing.T) {
	orderIds := []int64{2106526964}
	expressNumbers := []string{"SF1741594327"}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 测试按订单ID查询
	expressInfoList, err := NewClient().Search(ctx, orderIds, nil)
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("expressInfoList by orderIds: %v\n", expressInfoList)

	// 测试按快递单号查询
	expressInfoList2, err := NewClient().Search(ctx, nil, expressNumbers)
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("expressInfoList by expressNumbers: %v\n", expressInfoList2)
}

func TestClient_GetOneOpenList(t *testing.T) {
	userId := int64(2135330035)

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	oneOpenListResp, err := NewClient().GetOneOpenList(ctx, userId, []int64{}, 0)
	assert.NoError(t, err)
	fmt.Printf("oneOpenListResp: %v\n", oneOpenListResp)
}

func TestClient_SearchAfterPlat(t *testing.T) {
	userId := int64(2135330035)
	orderIds := []int64{2106669556}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	afterPlatResp, err := NewClient().SearchAfterPlat(ctx, userId, orderIds)
	assert.NoError(t, err)
	fmt.Printf("afterPlatResp: %v\n", afterPlatResp)
}

func TestClient_GetTradeList(t *testing.T) {
	userId := int64(2135330035)
	orderBusinessStatusList := []int{}
	offset := 0
	limit := 100
	orderBeginTime := time.Now().Unix() - 180*24*60*60
	orderEndTime := time.Now().Unix()

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	tradeListResp, err := NewClient().GetTradeList(ctx, userId, orderBusinessStatusList, orderBeginTime, orderEndTime, offset, limit, nil, nil)
	assert.NoError(t, err)
	fmt.Printf("tradeListResp: %v\n", tradeListResp)
}

func TestClient_GetTradeDetail(t *testing.T) {
	userId := int64(2135330035)
	orderIds := []int64{2106669556}
	//orderIds := []int64{2106667910, 2106667906, 2106663238, 2106649426, 2106649360, 2106642948, 2106642924, 2106624896, 2106618048, 2106612364, 2106607194, 2106606870, 2106592528, 2106590866, 2106585754, 2106585678, 2106580610, 2106572734, 2106567956, 2106547982, 2106545246, 2106541556, 2106534382, 2106528064, 2106527271, 2106527272, 2106526964, 2106526963, 2106526960, 2106526844, 2106523864, 2106523068, 2106523064, 2106522114, 2106521640, 2106521578, 2106521272, 2106520934, 2106518294, 2106518036, 2106512832, 2106512498, 2106474286, 2106474184, 2106472064, 2106471656, 2106469190}
	tradeFields := []string{
		"userId", "orderId", "orderTime", "payTime", "businessType",
		"payableAmount", "paidAmount", "orderStatus", "orderBusinessStatus",
		"logInfo", "skuRowList", "bindDetail", "addressInfo",
		"discountInfo", "currencyType", "assembleDetail",
	}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	tradeDetailList, err := NewClient().GetTradeDetail(ctx, userId, orderIds, tradeFields)
	assert.NoError(t, err)
	fmt.Printf("tradeDetailList: %v\n", tradeDetailList)
}

func TestClient_DarGetKVByCourseIds(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	fields := []string{
		"userId", "courseId", "tradeId", "subTradeId", "tradeFee", "tradeTime",
		"orderBusinessStatus", "refundStatus", "refundStartTime", "changeFromCourseId",
		"changeToCourseId", "changeTime", "createTime", "updateTime", "logInfo",
	}

	result, err := NewClient().DarGetKVByCourseIds(ctx, []string{"2135330035_89864"}, fields)
	assert.NoError(t, err)
	fmt.Printf("result: %v\n", result)
}