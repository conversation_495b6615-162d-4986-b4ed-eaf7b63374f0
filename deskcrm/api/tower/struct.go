package tower

const (
	NOT_INNER_COURSE = 0
	IS_INNER_COURSE  = 1
)

type CourseBind struct {
	CourseBindData CourseBindData `json:"courseBindData"`
}
type CourseBindData map[string][]CourseDataTower

type CourseDataTower struct {
	CourseID   int   `json:"courseId"`
	DeviceUID  int64 `json:"deviceUid"`
	Status     int   `json:"status"`
	MapID      int   `json:"mapId"`
	CreateTime int   `json:"createTime"`
}

type CourseInfo struct {
	ID               int    `json:"id"`
	CourseId         int64  `json:"courseId"`
	CourseName       string `json:"courseName" mapstructure:"courseName"`
	TeacherTagName   string `json:"teacherTagName"`
	GroupDesc        string `json:"groupDesc"`
	CreateTime       int    `json:"createTime"`
	UpdateTime       int    `json:"updateTime"`
	Status           int    `json:"status"`
	GroupServiceList []struct {
		ID               int    `json:"id"`
		GroupServiceName string `json:"groupServiceName"`
		IconURL          string `json:"iconUrl"`
	} `json:"groupServiceList"`
	TaskId          int    `json:"taskId"`
	CourseGroupType int    `json:"courseGroupType"`
	TaskStatus      int    `json:"taskStatus"`
	Subject         int    `json:"subject"`
	Grade           int    `json:"grade"`
	CoursePriceTag  int    `json:"coursePriceTag"`
	Period          int    `json:"period"`
	Year            int    `json:"year"`
	Season          int    `json:"season"`
	CourseType      int    `json:"courseType"`
	Ext             string `json:"ext"`
	Department      int    `json:"department"`
	SaleMode        int    `json:"saleMode"`
	IsInner         int    `json:"isInner"`
	OperatorUid     int    `json:"operatorUid"`
	Operator        string `json:"operator"`
	PullNewDuty     int    `json:"pullNewDuty"`
	NewCourseType   int    `json:"newCourseType"`
	Frequency       int    `json:"frequency"`
}

// ExpireTimeResponse 单个课程过期时间响应结构
type ExpireTimeResponse struct {
	ErrNo  int            `json:"errNo"`
	ErrMsg string         `json:"errMsg"`
	Data   ExpireTimeData `json:"data"`
}

// ExpireTimeData 过期时间数据
type ExpireTimeData struct {
	ExpireTime int64 `json:"expireTime"`
}

// BatchExpireTimeResponse 批量获取课程过期时间响应结构
type BatchExpireTimeResponse struct {
	ErrNo  int                       `json:"errNo"`
	ErrMsg string                    `json:"errMsg"`
	Data   map[string]ExpireTimeData `json:"data"`
}
