package tower

import (
	"deskcrm/api"
	"deskcrm/components"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"errors"
	"net/url"
	"strconv"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	getCourseBindByCourseIdsAPI  = "/tower/api/getcoursebindbycourseids"
	getCourseBindByDeviceUidsAPI = "/tower/api/getcoursebindbydeviceuids"
	getCourseInfoListAPI         = "/tower/api/getcourseinfolist"
	getExpireTimeByCourseAPI     = "/tower/api/getbatchexpiretimebycourselist"
	getExpireTimeByCourseIdAPI   = "/tower/api/getexpiretimebycourseid"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Tower,
	}
	return c
}

// 根据课程id查询排班信息
// https://yapi.zuoyebang.cc/project/5802/interface/api/201970
func (c *Client) GetCourseBindByCourseId(ctx *gin.Context, courseIds []int64) (bindData CourseBindData, err error) {

	if len(courseIds) == 0 {
		return nil, errors.New("param error")
	}
	resp := CourseBind{}

	req := map[string]interface{}{
		"courseIds": fwyyutils.JoinArrayInt64ToString(courseIds, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getCourseBindByCourseIdsAPI, opts)
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.CourseBindData, nil
}

// GetCourseInfo 批量获取课程信息
// https://yapi.zuoyebang.cc/project/5802/interface/api/212826
func (c *Client) GetCourseInfo(ctx *gin.Context, courseIds []int64) (courseInfoMap map[int64]*CourseInfo, err error) {
	if len(courseIds) == 0 {
		return nil, errors.New("param error")
	}

	v := url.Values{}
	for _, courseId := range courseIds {
		v.Add("courseIds[]", strconv.Itoa(int(courseId)))
	}

	req := map[string]interface{}{}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getCourseInfoListAPI+"?"+v.Encode(), opts)
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := make([]*CourseInfo, 0, len(courseIds))
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	courseInfoMap = map[int64]*CourseInfo{}
	for idx := range resp {
		courseInfoMap[resp[idx].CourseId] = resp[idx]
	}

	return courseInfoMap, nil
}

// GetCourseBindByDeviceUid 根据资产查课程
func (c *Client) GetCourseBindByDeviceUid(ctx *gin.Context, deviceUids []int64, year int64) (bindData CourseBindData, err error) {

	if len(deviceUids) == 0 {
		return nil, errors.New("param error")
	}
	resp := CourseBind{}

	req := map[string]interface{}{
		"deviceUids": fwyyutils.JoinArrayInt64ToString(deviceUids, ","),
		"year":       year,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getCourseBindByDeviceUidsAPI, opts)
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.CourseBindData, nil
}

// GetExpireTimeByCourse 根据单个课程ID获取过期时间
// 对应PHP的Api_Tower::getExpireTimeByCourse方法
// https://yapi.zuoyebang.cc/project/5802/interface/api/xxx
func (c *Client) GetExpireTimeByCourse(ctx *gin.Context, courseId int64) (int64, error) {
	if courseId <= 0 {
		return 0, errors.New("courseId不能为空")
	}

	req := map[string]interface{}{
		"courseId": courseId,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getExpireTimeByCourseIdAPI, opts)
	if err != nil {
		return 0, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return 0, err
	}

	var resp ExpireTimeResponse
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return 0, err
	}

	return resp.Data.ExpireTime, nil
}

// GetBatchExpireTimeByCourseList 批量获取课程过期时间
// 对应PHP的Api_Tower::getExpireTimeByCourse方法的批量版本
func (c *Client) GetBatchExpireTimeByCourseList(ctx *gin.Context, courseIds []int64) (map[int64]int64, error) {
	if len(courseIds) == 0 {
		return map[int64]int64{}, nil
	}

	req := map[string]interface{}{
		"courseIds": components.Array.JoinArrayInt64ToString(courseIds, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getExpireTimeByCourseAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetBatchExpireTimeByCourseList request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp BatchExpireTimeResponse
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	// 转换响应数据格式
	result := make(map[int64]int64)
	for courseIdStr, expireData := range resp.Data {
		if courseId, parseErr := strconv.ParseInt(courseIdStr, 10, 64); parseErr == nil {
			result[courseId] = expireData.ExpireTime
		}
	}

	return result, nil
}
