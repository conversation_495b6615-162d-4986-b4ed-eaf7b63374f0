package outputStudent

// CourseRecordMetaResponse 课程记录元数据响应
// 对应PHP的Service_Page_DeskV1_Student_CourseRecordMeta接口响应
type CourseRecordMetaResponse struct {
	TotalCourses        int     `json:"totalCourses"`        // 总课程数
	CurrentPeriodCourse int     `json:"currentPeriodCourse"` // 当前期课程数
	TotalPaymentAmount  string  `json:"totalPaymentAmount"`  // 总付费金额（格式化为字符串）
	AttendedLessons     int     `json:"attendedLessons"`     // 已上课节数
	FinishedLessons     int     `json:"finishedLessons"`     // 已完成课节数
	PlaybackLessons     int     `json:"playbackLessons"`     // 回放课节数
}
