package inputStudent

import (
	"fmt"
	"github.com/gin-gonic/gin"
)

// CourseRecordMetaParam 课程记录元数据请求参数
// 对应PHP的Service_Page_DeskV1_Student_CourseRecordMeta接口参数
type CourseRecordMetaParam struct {
	StudentUid int64 `json:"studentUid" form:"studentUid" binding:"required"` // 学生UID
	PersonUid  int64 `json:"personUid" form:"personUid" binding:"required"`   // 人员UID（LPC）
	CourseId   int64 `json:"courseId" form:"courseId" binding:"required"`     // 课程ID
}

// Validate 参数验证
func (p *CourseRecordMetaParam) Validate(ctx *gin.Context) error {
	if p.StudentUid <= 0 {
		return fmt.Errorf("studentUid不能为空或小于等于0")
	}
	
	if p.PersonUid <= 0 {
		return fmt.Errorf("personUid不能为空或小于等于0")
	}
	
	if p.CourseId <= 0 {
		return fmt.Errorf("courseId不能为空或小于等于0")
	}
	
	return nil
}
